"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/lean-gpt";
exports.ids = ["pages/api/lean-gpt"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "openai":
/*!*************************!*\
  !*** external "openai" ***!
  \*************************/
/***/ ((module) => {

module.exports = import("openai");;

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("child_process");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("fs/promises");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Flean-gpt&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Flean-gpt.js&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Flean-gpt&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Flean-gpt.js&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_lean_gpt_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/lean-gpt.js */ \"(api)/./pages/api/lean-gpt.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_pages_api_lean_gpt_js__WEBPACK_IMPORTED_MODULE_3__]);\n_pages_api_lean_gpt_js__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_lean_gpt_js__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_lean_gpt_js__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/lean-gpt\",\n        pathname: \"/api/lean-gpt\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_lean_gpt_js__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTX0FQSSZwYWdlPSUyRmFwaSUyRmxlYW4tZ3B0JnByZWZlcnJlZFJlZ2lvbj0mYWJzb2x1dGVQYWdlUGF0aD0uJTJGcGFnZXMlMkZhcGklMkZsZWFuLWdwdC5qcyZtaWRkbGV3YXJlQ29uZmlnQmFzZTY0PWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBc0c7QUFDdkM7QUFDTDtBQUMxRDtBQUNvRDtBQUNwRDtBQUNBLGlFQUFlLHdFQUFLLENBQUMsbURBQVEsWUFBWSxFQUFDO0FBQzFDO0FBQ08sZUFBZSx3RUFBSyxDQUFDLG1EQUFRO0FBQ3BDO0FBQ08sd0JBQXdCLGdIQUFtQjtBQUNsRDtBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxZQUFZO0FBQ1osQ0FBQzs7QUFFRCxxQyIsInNvdXJjZXMiOlsid2VicGFjazovL2xlYW5hdG9tLz80OGU5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBhZ2VzQVBJUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9wYWdlcy1hcGkvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgaG9pc3QgfSBmcm9tIFwibmV4dC9kaXN0L2J1aWxkL3RlbXBsYXRlcy9oZWxwZXJzXCI7XG4vLyBJbXBvcnQgdGhlIHVzZXJsYW5kIGNvZGUuXG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiLi9wYWdlcy9hcGkvbGVhbi1ncHQuanNcIjtcbi8vIFJlLWV4cG9ydCB0aGUgaGFuZGxlciAoc2hvdWxkIGJlIHRoZSBkZWZhdWx0IGV4cG9ydCkuXG5leHBvcnQgZGVmYXVsdCBob2lzdCh1c2VybGFuZCwgXCJkZWZhdWx0XCIpO1xuLy8gUmUtZXhwb3J0IGNvbmZpZy5cbmV4cG9ydCBjb25zdCBjb25maWcgPSBob2lzdCh1c2VybGFuZCwgXCJjb25maWdcIik7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBQYWdlc0FQSVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5QQUdFU19BUEksXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9sZWFuLWdwdFwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL2xlYW4tZ3B0XCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogXCJcIixcbiAgICAgICAgZmlsZW5hbWU6IFwiXCJcbiAgICB9LFxuICAgIHVzZXJsYW5kXG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGFnZXMtYXBpLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Flean-gpt&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Flean-gpt.js&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./lib/leanVerifier.js":
/*!*****************************!*\
  !*** ./lib/leanVerifier.js ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LeanVerifier: () => (/* binding */ LeanVerifier),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! child_process */ \"child_process\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(child_process__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! util */ \"util\");\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(util__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst execAsync = (0,util__WEBPACK_IMPORTED_MODULE_1__.promisify)(child_process__WEBPACK_IMPORTED_MODULE_0__.exec);\n/**\n * Lean 代码验证器类\n */ class LeanVerifier {\n    constructor(){\n        this.tempDir = path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), \"temp\");\n        this.leanProjectDir = path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), \"lean\");\n    }\n    /**\n   * 确保临时目录存在\n   */ async ensureTempDir() {\n        try {\n            await fs_promises__WEBPACK_IMPORTED_MODULE_2___default().mkdir(this.tempDir, {\n                recursive: true\n            });\n        } catch (error) {\n            console.error(\"Failed to create temp directory:\", error);\n        }\n    }\n    /**\n   * 验证 Lean 代码\n   * @param {string} leanCode - 要验证的 Lean 代码\n   * @param {string} filename - 文件名（可选）\n   * @returns {Promise<Object>} 验证结果\n   */ async verifyCode(leanCode, filename = null) {\n        await this.ensureTempDir();\n        const tempFileName = filename || `temp_${Date.now()}.lean`;\n        const tempFile = path__WEBPACK_IMPORTED_MODULE_3___default().join(this.tempDir, tempFileName);\n        try {\n            // 写入临时文件\n            await fs_promises__WEBPACK_IMPORTED_MODULE_2___default().writeFile(tempFile, leanCode);\n            // 执行 Lean 验证\n            const result = await this.runLeanCheck(tempFile);\n            // 清理临时文件\n            await this.cleanupTempFile(tempFile);\n            return result;\n        } catch (error) {\n            // 确保清理临时文件\n            await this.cleanupTempFile(tempFile);\n            throw error;\n        }\n    }\n    /**\n   * 运行 Lean 检查\n   * @param {string} filePath - 文件路径\n   * @returns {Promise<Object>} 检查结果\n   */ async runLeanCheck(filePath) {\n        try {\n            // 尝试使用 lean 命令检查文件\n            const { stdout, stderr } = await execAsync(`lean --check \"${filePath}\"`, {\n                timeout: 30000,\n                cwd: this.leanProjectDir\n            });\n            return {\n                success: stderr === \"\",\n                output: stdout,\n                error: stderr,\n                hasErrors: stderr !== \"\",\n                hasWarnings: stdout.includes(\"warning\"),\n                executionTime: Date.now()\n            };\n        } catch (error) {\n            // 处理超时和其他错误\n            if (error.killed && error.signal === \"SIGTERM\") {\n                return {\n                    success: false,\n                    output: \"\",\n                    error: \"Lean verification timed out (30s)\",\n                    hasErrors: true,\n                    hasWarnings: false,\n                    executionTime: Date.now()\n                };\n            }\n            return {\n                success: false,\n                output: error.stdout || \"\",\n                error: error.stderr || error.message,\n                hasErrors: true,\n                hasWarnings: false,\n                executionTime: Date.now()\n            };\n        }\n    }\n    /**\n   * 清理临时文件\n   * @param {string} filePath - 要删除的文件路径\n   */ async cleanupTempFile(filePath) {\n        try {\n            await fs_promises__WEBPACK_IMPORTED_MODULE_2___default().unlink(filePath);\n        } catch (error) {\n            // 忽略文件不存在的错误\n            if (error.code !== \"ENOENT\") {\n                console.error(\"Failed to cleanup temp file:\", error);\n            }\n        }\n    }\n    /**\n   * 验证 Lean 代码语法\n   * @param {string} leanCode - Lean 代码\n   * @returns {Object} 语法检查结果\n   */ validateSyntax(leanCode) {\n        const issues = [];\n        // 基本语法检查\n        if (!leanCode.trim()) {\n            issues.push(\"Empty Lean code\");\n            return {\n                valid: false,\n                issues\n            };\n        }\n        // 检查是否包含基本结构\n        const hasImports = /^import\\s+/.test(leanCode);\n        const hasDefinitions = /\\b(def|theorem|lemma|example)\\s+/.test(leanCode);\n        if (!hasImports) {\n            issues.push(\"Missing import statements\");\n        }\n        if (!hasDefinitions) {\n            issues.push(\"No definitions, theorems, or lemmas found\");\n        }\n        // 检查括号匹配\n        const openParens = (leanCode.match(/\\(/g) || []).length;\n        const closeParens = (leanCode.match(/\\)/g) || []).length;\n        if (openParens !== closeParens) {\n            issues.push(\"Mismatched parentheses\");\n        }\n        // 检查大括号匹配\n        const openBraces = (leanCode.match(/\\{/g) || []).length;\n        const closeBraces = (leanCode.match(/\\}/g) || []).length;\n        if (openBraces !== closeBraces) {\n            issues.push(\"Mismatched braces\");\n        }\n        return {\n            valid: issues.length === 0,\n            issues: issues,\n            hasImports,\n            hasDefinitions\n        };\n    }\n    /**\n   * 提取 Lean 代码中的定理和定义\n   * @param {string} leanCode - Lean 代码\n   * @returns {Object} 提取的信息\n   */ extractInfo(leanCode) {\n        const theorems = [];\n        const definitions = [];\n        const imports = [];\n        // 提取 import 语句\n        const importMatches = leanCode.match(/^import\\s+(.+)$/gm);\n        if (importMatches) {\n            imports.push(...importMatches.map((match)=>match.replace(/^import\\s+/, \"\")));\n        }\n        // 提取定理\n        const theoremMatches = leanCode.match(/theorem\\s+(\\w+)[^:]*:/g);\n        if (theoremMatches) {\n            theorems.push(...theoremMatches.map((match)=>match.match(/theorem\\s+(\\w+)/)[1]));\n        }\n        // 提取定义\n        const defMatches = leanCode.match(/def\\s+(\\w+)[^:]*:/g);\n        if (defMatches) {\n            definitions.push(...defMatches.map((match)=>match.match(/def\\s+(\\w+)/)[1]));\n        }\n        return {\n            imports,\n            theorems,\n            definitions,\n            totalLines: leanCode.split(\"\\n\").length\n        };\n    }\n}\n// 创建单例实例\nconst leanVerifier = new LeanVerifier();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (leanVerifier);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/leanVerifier.js\n");

/***/ }),

/***/ "(api)/./lib/promptTemplates.js":
/*!********************************!*\
  !*** ./lib/promptTemplates.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ERROR_FIXING_PROMPT: () => (/* binding */ ERROR_FIXING_PROMPT),\n/* harmony export */   GEOCHEMISTRY_PROMPT: () => (/* binding */ GEOCHEMISTRY_PROMPT),\n/* harmony export */   INTERACTIVE_QA_PROMPT: () => (/* binding */ INTERACTIVE_QA_PROMPT),\n/* harmony export */   LEAN_CONVERSION_PROMPT: () => (/* binding */ LEAN_CONVERSION_PROMPT),\n/* harmony export */   NUMERICAL_VERIFICATION_PROMPT: () => (/* binding */ NUMERICAL_VERIFICATION_PROMPT),\n/* harmony export */   detectQuestionType: () => (/* binding */ detectQuestionType),\n/* harmony export */   formatConversationHistory: () => (/* binding */ formatConversationHistory),\n/* harmony export */   getPromptTemplate: () => (/* binding */ getPromptTemplate)\n/* harmony export */ });\n/**\n * GPT 提示词模板管理\n */ // 基础 Lean 转换提示词\nconst LEAN_CONVERSION_PROMPT = `你是一个专业的 Lean 4 数学证明助手。请将用户的自然语言问题转换为 Lean 4 代码。\n\n用户问题: {question}\n\n请按照以下格式回复:\n1. 首先分析问题的数学本质\n2. 定义相关的常数和函数\n3. 写出要证明的定理\n4. 提供证明思路（可以使用 sorry 作为占位符）\n\n请确保生成的 Lean 代码符合 Lean 4 语法，包含必要的 import 语句。\n\n示例格式:\n\\`\\`\\`lean\nimport Mathlib.Analysis.SpecialFunctions.Exp\nimport Mathlib.Data.Real.Basic\n\n-- 常数定义\ndef v : ℝ := 0.01  -- 流速 (m/yr)\n\n-- 函数定义\ndef concentration (t : ℝ) : ℝ := c0 * Real.exp (-λ * t / R)\n\n-- 定理\ntheorem main_theorem : concentration 10000 ≤ 0.015 := by\n  sorry\n\\`\\`\\`\n\n注意事项:\n- 使用 Lean 4 语法 (Real.exp 而不是 real.exp)\n- 包含适当的 import 语句\n- 提供清晰的注释\n- 使用合理的变量名`;\n// 地球化学专用提示词\nconst GEOCHEMISTRY_PROMPT = `你是一个专业的地球化学建模和 Lean 4 证明助手。专门处理与地下水、核素迁移、化学反应相关的问题。\n\n用户问题: {question}\n\n请分析这个地球化学问题并转换为 Lean 4 代码:\n\n1. **问题分析**: 识别涉及的物理/化学过程\n2. **参数定义**: 定义相关的物理常数和初始条件\n3. **数学建模**: 建立微分方程或代数方程\n4. **Lean 实现**: 转换为 Lean 4 代码\n5. **验证目标**: 明确要证明的性质\n\n常见的地球化学参数:\n- 流速 (v): m/yr\n- 衰变常数 (λ): 1/yr  \n- 滞留因子 (R): 无量纲\n- 浓度 (c): mg/L\n- 扩散系数 (D): m²/yr\n- 吸附系数 (Kd): L/kg\n\n请生成符合 Lean 4 语法的代码:\n\\`\\`\\`lean\nimport Mathlib.Analysis.SpecialFunctions.Exp\nimport Mathlib.Data.Real.Basic\nimport Mathlib.Analysis.Calculus.FDeriv.Basic\n\n-- 物理参数定义\ndef v : ℝ := 0.01       -- 流速\ndef λ : ℝ := 1e-5       -- 衰变常数\ndef R : ℝ := 1.2        -- 滞留因子\n\n-- 建模函数\ndef concentration (t : ℝ) : ℝ := ...\n\n-- 要证明的定理\ntheorem safety_bound : ∀ t ∈ Set.Icc 0 10000, concentration t ≤ threshold := by\n  sorry\n\\`\\`\\``;\n// 数值计算验证提示词\nconst NUMERICAL_VERIFICATION_PROMPT = `请为以下 Lean 代码提供数值验证和计算辅助:\n\nLean 代码:\n{leanCode}\n\n请提供:\n1. **数值计算**: 计算关键数值结果\n2. **边界检查**: 验证是否满足安全限值\n3. **敏感性分析**: 分析参数变化的影响\n4. **物理解释**: 解释结果的物理意义\n\n请使用 Python 代码进行数值验证:\n\\`\\`\\`python\nimport numpy as np\nimport matplotlib.pyplot as plt\n\n# 参数定义\nv = 0.01\nlambda_decay = 1e-5\nR = 1.2\nc0 = 0.1\n\n# 浓度函数\ndef concentration(t):\n    return c0 * np.exp(-lambda_decay * t / R)\n\n# 验证计算\nt_target = 10000\nc_result = concentration(t_target)\nprint(f\"在 {t_target} 年时的浓度: {c_result:.6f} mg/L\")\n\\`\\`\\``;\n// 错误修复提示词\nconst ERROR_FIXING_PROMPT = `以下 Lean 代码存在错误，请帮助修复:\n\n错误的 Lean 代码:\n{leanCode}\n\n错误信息:\n{errorMessage}\n\n请提供:\n1. **错误分析**: 解释错误的原因\n2. **修复方案**: 提供正确的代码\n3. **语法说明**: 解释正确的 Lean 4 语法\n4. **最佳实践**: 避免类似错误的建议\n\n修复后的代码应该:\n- 符合 Lean 4 语法规范\n- 包含正确的 import 语句\n- 使用适当的类型注解\n- 提供清晰的注释`;\n// 交互式问答提示词\nconst INTERACTIVE_QA_PROMPT = `基于之前的对话历史，回答用户的后续问题:\n\n对话历史:\n{conversationHistory}\n\n当前问题: {question}\n\n请提供:\n1. **上下文理解**: 基于之前的讨论\n2. **问题回答**: 直接回答用户问题\n3. **代码更新**: 如需要，更新 Lean 代码\n4. **进一步探索**: 建议相关的后续问题\n\n如果问题涉及参数变化或假设情况，请:\n- 修改相应的 Lean 定义\n- 重新分析数学模型\n- 提供新的证明目标`;\n/**\n * 根据问题类型选择合适的提示词模板\n * @param {string} questionType - 问题类型\n * @param {Object} context - 上下文信息\n * @returns {string} 格式化的提示词\n */ function getPromptTemplate(questionType, context = {}) {\n    const templates = {\n        \"lean_conversion\": LEAN_CONVERSION_PROMPT,\n        \"geochemistry\": GEOCHEMISTRY_PROMPT,\n        \"numerical_verification\": NUMERICAL_VERIFICATION_PROMPT,\n        \"error_fixing\": ERROR_FIXING_PROMPT,\n        \"interactive_qa\": INTERACTIVE_QA_PROMPT\n    };\n    let template = templates[questionType] || LEAN_CONVERSION_PROMPT;\n    // 替换模板中的占位符\n    Object.keys(context).forEach((key)=>{\n        const placeholder = `{${key}}`;\n        template = template.replace(new RegExp(placeholder, \"g\"), context[key] || \"\");\n    });\n    return template;\n}\n/**\n * 检测问题类型\n * @param {string} question - 用户问题\n * @param {Array} conversationHistory - 对话历史\n * @returns {string} 问题类型\n */ function detectQuestionType(question, conversationHistory = []) {\n    const lowerQuestion = question.toLowerCase();\n    // 检查是否是交互式问答\n    if (conversationHistory.length > 0) {\n        if (lowerQuestion.includes(\"如果\") || lowerQuestion.includes(\"假设\") || lowerQuestion.includes(\"那么\") || lowerQuestion.includes(\"会怎样\")) {\n            return \"interactive_qa\";\n        }\n    }\n    // 检查是否是地球化学相关\n    const geochemKeywords = [\n        \"浓度\",\n        \"流速\",\n        \"衰变\",\n        \"核素\",\n        \"地下水\",\n        \"迁移\",\n        \"吸附\",\n        \"扩散\"\n    ];\n    if (geochemKeywords.some((keyword)=>lowerQuestion.includes(keyword))) {\n        return \"geochemistry\";\n    }\n    // 检查是否是数值验证\n    if (lowerQuestion.includes(\"计算\") || lowerQuestion.includes(\"验证\") || lowerQuestion.includes(\"数值\") || lowerQuestion.includes(\"结果\")) {\n        return \"numerical_verification\";\n    }\n    // 默认为 Lean 转换\n    return \"lean_conversion\";\n}\n/**\n * 格式化对话历史\n * @param {Array} messages - 消息数组\n * @returns {string} 格式化的对话历史\n */ function formatConversationHistory(messages) {\n    return messages.map((msg, index)=>{\n        const role = msg.role === \"user\" ? \"用户\" : \"助手\";\n        return `${index + 1}. ${role}: ${msg.text}`;\n    }).join(\"\\n\\n\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/promptTemplates.js\n");

/***/ }),

/***/ "(api)/./pages/api/lean-gpt.js":
/*!*******************************!*\
  !*** ./pages/api/lean-gpt.js ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"openai\");\n/* harmony import */ var _lib_leanVerifier_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../lib/leanVerifier.js */ \"(api)/./lib/leanVerifier.js\");\n/* harmony import */ var _lib_promptTemplates_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../lib/promptTemplates.js */ \"(api)/./lib/promptTemplates.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([openai__WEBPACK_IMPORTED_MODULE_0__]);\nopenai__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// 初始化 OpenAI 客户端\nconst openai = new openai__WEBPACK_IMPORTED_MODULE_0__.OpenAI({\n    apiKey: process.env.OPENAI_API_KEY\n});\n// 生成模拟响应函数\nfunction generateMockResponse(question, questionType) {\n    const lowerQuestion = question.toLowerCase();\n    if (lowerQuestion.includes(\"铀\") || lowerQuestion.includes(\"浓度\") || lowerQuestion.includes(\"衰变\")) {\n        return `## 铀浓度衰变模型分析\n\n基于您的问题，我将建立一个描述铀浓度随时间衰变的数学模型。\n\n### 数学模型分析\n这是一个典型的放射性衰变问题，遵循指数衰减规律。考虑到地下水环境中的滞留因子，我们需要建立修正的衰变方程。\n\n### Lean 4 代码实现\n\n\\`\\`\\`lean\nimport Mathlib.Analysis.SpecialFunctions.Exp\nimport Mathlib.Data.Real.Basic\n\n-- 物理参数定义\ndef v : ℝ := 0.01       -- 流速 (m/yr)\ndef λ : ℝ := 1e-5       -- 衰变常数 (1/yr)\ndef R : ℝ := 1.2        -- 滞留因子\ndef c0 : ℝ := 0.1       -- 初始浓度 (mg/L)\n\n-- 浓度函数定义：考虑滞留因子的衰变模型\ndef uranium_concentration (t : ℝ) : ℝ :=\n  c0 * Real.exp (-λ * t / R)\n\n-- 安全性定理：10000年后浓度低于限值\ntheorem uranium_safety : uranium_concentration 10000 ≤ 0.015 := by\n  unfold uranium_concentration\n  -- 数值计算：0.1 * exp(-1e-5 * 10000 / 1.2) ≈ 0.0147 < 0.015\n  sorry\n\n-- 单调性定理：浓度随时间单调递减\ntheorem concentration_decreasing :\n  ∀ t₁ t₂ : ℝ, 0 ≤ t₁ → t₁ ≤ t₂ →\n  uranium_concentration t₂ ≤ uranium_concentration t₁ := by\n  sorry\n\\`\\`\\`\n\n### 模型说明\n1. **衰变常数**: λ = 1×10⁻⁵ /yr（铀的典型衰变常数）\n2. **滞留因子**: R = 1.2（考虑土壤吸附作用）\n3. **安全限值**: 0.015 mg/L（环境标准）\n\n### 验证结果\n- 在10000年时，铀浓度约为 0.0147 mg/L\n- 满足安全限值要求（< 0.015 mg/L）\n- 模型通过了 Lean 4 的形式化验证\n\n*注：当前为演示模式，使用模拟响应。配置有效的 OpenAI API Key 后将获得更智能的回答。*`;\n    }\n    if (lowerQuestion.includes(\"流速\") || lowerQuestion.includes(\"地下水\")) {\n        return `## 地下水流动模型\n\n### 问题分析\n您询问的是地下水流动相关问题。我将基于达西定律建立相应的数学模型。\n\n\\`\\`\\`lean\nimport Mathlib.Data.Real.Basic\n\n-- 达西定律参数\ndef K : ℝ := 1e-5      -- 渗透系数 (m/s)\ndef i : ℝ := 0.01      -- 水力梯度\ndef n : ℝ := 0.3       -- 孔隙度\n\n-- 达西流速\ndef darcy_velocity : ℝ := K * i\n\n-- 实际流速\ndef actual_velocity : ℝ := darcy_velocity / n\n\ntheorem velocity_relation : actual_velocity = K * i / n := by\n  unfold actual_velocity darcy_velocity\n  rfl\n\\`\\`\\`\n\n*演示模式响应 - 请配置 OpenAI API Key 获得完整功能*`;\n    }\n    // 默认响应\n    return `## 地球化学建模分析\n\n感谢您的问题！我正在分析您的地球化学问题并生成相应的 Lean 4 代码。\n\n### 问题类型\n检测到问题类型：${questionType}\n\n### 示例 Lean 代码\n\n\\`\\`\\`lean\nimport Mathlib.Data.Real.Basic\n\n-- 基础参数定义\ndef example_parameter : ℝ := 1.0\n\n-- 示例函数\ndef example_function (t : ℝ) : ℝ := example_parameter * t\n\n-- 示例定理\ntheorem example_theorem : example_function 0 = 0 := by\n  unfold example_function\n  simp\n\\`\\`\\`\n\n### 说明\n这是一个演示响应。要获得针对您具体问题的智能分析和完整的 Lean 代码，请：\n\n1. 配置有效的 OpenAI API Key\n2. 确保 API Key 有足够的使用额度\n3. 重新启动应用\n\n*当前为演示模式*`;\n}\nasync function handler(req, res) {\n    // 检查请求方法\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    const { question, conversationHistory = [] } = req.body;\n    // 验证输入\n    if (!question || typeof question !== \"string\") {\n        return res.status(400).json({\n            error: \"Question is required and must be a string\"\n        });\n    }\n    try {\n        // 检测问题类型\n        const questionType = (0,_lib_promptTemplates_js__WEBPACK_IMPORTED_MODULE_2__.detectQuestionType)(question, conversationHistory);\n        // 准备上下文信息\n        const context = {\n            question: question,\n            conversationHistory: (0,_lib_promptTemplates_js__WEBPACK_IMPORTED_MODULE_2__.formatConversationHistory)(conversationHistory)\n        };\n        // 获取合适的提示词模板\n        const prompt = (0,_lib_promptTemplates_js__WEBPACK_IMPORTED_MODULE_2__.getPromptTemplate)(questionType, context);\n        let gptResponse;\n        // 检查是否有有效的 OpenAI API Key\n        if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY.startsWith(\"your_\")) {\n            // 演示模式 - 生成模拟响应\n            gptResponse = generateMockResponse(question, questionType);\n        } else {\n            try {\n                const completion = await openai.chat.completions.create({\n                    model: \"gpt-3.5-turbo\",\n                    messages: [\n                        {\n                            role: \"system\",\n                            content: \"你是一个专业的 Lean 4 数学证明助手，专门帮助用户将自然语言问题转换为 Lean 代码，特别擅长地球化学和环境科学建模。\"\n                        },\n                        {\n                            role: \"user\",\n                            content: prompt\n                        }\n                    ],\n                    max_tokens: 2000,\n                    temperature: 0.3\n                });\n                gptResponse = completion.choices[0].message.content;\n            } catch (apiError) {\n                console.log(\"OpenAI API Error, falling back to mock response:\", apiError.message);\n                gptResponse = generateMockResponse(question, questionType);\n            }\n        }\n        // 提取 Lean 代码\n        const leanCodeMatch = gptResponse.match(/```lean\\n([\\s\\S]*?)\\n```/);\n        let leanCode = \"\";\n        let verificationResult = null;\n        let syntaxValidation = null;\n        let codeInfo = null;\n        if (leanCodeMatch) {\n            leanCode = leanCodeMatch[1];\n            // 语法验证\n            syntaxValidation = _lib_leanVerifier_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].validateSyntax(leanCode);\n            // 提取代码信息\n            codeInfo = _lib_leanVerifier_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].extractInfo(leanCode);\n            // Lean 代码验证 (如果启用)\n            if (process.env.ENABLE_LEAN_VERIFICATION === \"true\") {\n                try {\n                    verificationResult = await _lib_leanVerifier_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].verifyCode(leanCode);\n                } catch (error) {\n                    verificationResult = {\n                        success: false,\n                        error: error.message,\n                        output: \"\"\n                    };\n                }\n            }\n        }\n        // 返回结果\n        res.status(200).json({\n            answer: gptResponse,\n            leanCode: leanCode,\n            questionType: questionType,\n            syntaxValidation: syntaxValidation,\n            codeInfo: codeInfo,\n            verification: verificationResult,\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"API Error:\", error);\n        // 处理不同类型的错误\n        if (error.code === \"insufficient_quota\") {\n            return res.status(429).json({\n                error: \"OpenAI API quota exceeded\"\n            });\n        }\n        if (error.code === \"invalid_api_key\") {\n            return res.status(401).json({\n                error: \"Invalid OpenAI API key\"\n            });\n        }\n        res.status(500).json({\n            error: \"Internal server error\",\n            message:  true ? error.message : 0\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/lean-gpt.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Flean-gpt&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Flean-gpt.js&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();