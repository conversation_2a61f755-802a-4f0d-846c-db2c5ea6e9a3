"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./node_modules/next/dist/pages/_app.js\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _pages_index_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/index.js */ \"./pages/index.js\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: (private_next_pages_app__WEBPACK_IMPORTED_MODULE_4___default()),\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_index_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/LeanGptAssistant.js":
/*!****************************************!*\
  !*** ./components/LeanGptAssistant.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LeanGptAssistant)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction LeanGptAssistant() {\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            role: \"system\",\n            text: \"欢迎使用 LeanAtom 助手！我可以帮您将地球化学和环境科学问题转换为 Lean 4 数学证明。请输入您的问题。\",\n            timestamp: new Date().toISOString()\n        }\n    ]);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLeanCode, setShowLeanCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 自动滚动到底部\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    }, [\n        messages\n    ]);\n    async function handleAsk() {\n        if (!input.trim()) return;\n        setLoading(true);\n        const userMessage = {\n            role: \"user\",\n            text: input,\n            timestamp: new Date().toISOString()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        try {\n            const res = await fetch(\"/api/lean-gpt\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    question: input,\n                    conversationHistory: messages.filter((m)=>m.role !== \"system\")\n                })\n            });\n            if (!res.ok) {\n                throw new Error(`HTTP error! status: ${res.status}`);\n            }\n            const data = await res.json();\n            const reply = {\n                role: \"assistant\",\n                text: data.answer,\n                leanCode: data.leanCode,\n                questionType: data.questionType,\n                syntaxValidation: data.syntaxValidation,\n                codeInfo: data.codeInfo,\n                verification: data.verification,\n                timestamp: data.timestamp\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    reply\n                ]);\n        } catch (error) {\n            console.error(\"Error:\", error);\n            const errorMessage = {\n                role: \"assistant\",\n                text: `抱歉，发生了错误：${error.message}。请检查网络连接或稍后重试。`,\n                timestamp: new Date().toISOString(),\n                isError: true\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        }\n        setInput(\"\");\n        setLoading(false);\n    }\n    // 处理键盘事件\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleAsk();\n        }\n    };\n    // 清空对话\n    const clearConversation = ()=>{\n        setMessages([\n            {\n                role: \"system\",\n                text: \"对话已清空。请输入新的问题。\",\n                timestamp: new Date().toISOString()\n            }\n        ]);\n    };\n    // 渲染消息组件\n    const renderMessage = (msg, index)=>{\n        const isUser = msg.role === \"user\";\n        const isSystem = msg.role === \"system\";\n        const isError = msg.isError;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                marginBottom: \"1rem\",\n                display: \"flex\",\n                justifyContent: isUser ? \"flex-end\" : \"flex-start\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: \"80%\",\n                    padding: \"0.75rem 1rem\",\n                    borderRadius: \"1rem\",\n                    backgroundColor: isUser ? \"#007bff\" : isSystem ? \"#f8f9fa\" : isError ? \"#f8d7da\" : \"#e9ecef\",\n                    color: isUser ? \"white\" : isError ? \"#721c24\" : \"#333\",\n                    border: isError ? \"1px solid #f5c6cb\" : \"none\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"0.875rem\",\n                            marginBottom: \"0.5rem\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: isUser ? \"用户\" : isSystem ? \"系统\" : \"LeanAtom 助手\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this),\n                            msg.timestamp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    marginLeft: \"0.5rem\",\n                                    opacity: 0.7\n                                },\n                                children: new Date(msg.timestamp).toLocaleTimeString()\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n                                lineNumber: 116,\n                                columnNumber: 15\n                            }, this),\n                            msg.questionType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    marginLeft: \"0.5rem\",\n                                    padding: \"0.125rem 0.375rem\",\n                                    backgroundColor: \"rgba(255,255,255,0.2)\",\n                                    borderRadius: \"0.25rem\",\n                                    fontSize: \"0.75rem\"\n                                },\n                                children: msg.questionType\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n                                lineNumber: 121,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            whiteSpace: \"pre-wrap\",\n                            lineHeight: \"1.5\"\n                        },\n                        children: msg.text\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this),\n                    msg.leanCode && showLeanCode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginTop: \"1rem\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"0.875rem\",\n                                    fontWeight: \"bold\",\n                                    marginBottom: \"0.5rem\",\n                                    color: \"#495057\"\n                                },\n                                children: \"生成的 Lean 代码:\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n                                lineNumber: 140,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                style: {\n                                    backgroundColor: \"#f8f9fa\",\n                                    padding: \"0.75rem\",\n                                    borderRadius: \"0.5rem\",\n                                    fontSize: \"0.875rem\",\n                                    overflow: \"auto\",\n                                    border: \"1px solid #dee2e6\"\n                                },\n                                children: msg.leanCode\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n                                lineNumber: 148,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n                        lineNumber: 139,\n                        columnNumber: 13\n                    }, this),\n                    msg.verification && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginTop: \"0.75rem\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: \"0.5rem\",\n                                borderRadius: \"0.375rem\",\n                                backgroundColor: msg.verification.success ? \"#d4edda\" : \"#f8d7da\",\n                                border: `1px solid ${msg.verification.success ? \"#c3e6cb\" : \"#f5c6cb\"}`,\n                                fontSize: \"0.875rem\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Lean 验证: \"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n                                    lineNumber: 171,\n                                    columnNumber: 17\n                                }, this),\n                                msg.verification.success ? \"✅ 通过\" : \"❌ 失败\",\n                                msg.verification.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginTop: \"0.25rem\",\n                                        color: \"#721c24\"\n                                    },\n                                    children: msg.verification.error\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n                                    lineNumber: 174,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n                            lineNumber: 164,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n                        lineNumber: 163,\n                        columnNumber: 13\n                    }, this),\n                    msg.codeInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginTop: \"0.75rem\",\n                            fontSize: \"0.75rem\",\n                            opacity: 0.8\n                        },\n                        children: [\n                            \"定理: \",\n                            msg.codeInfo.theorems.length,\n                            \" | 定义: \",\n                            msg.codeInfo.definitions.length,\n                            \" | 导入: \",\n                            msg.codeInfo.imports.length,\n                            \" | 行数: \",\n                            msg.codeInfo.totalLines\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n                        lineNumber: 184,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n                lineNumber: 105,\n                columnNumber: 9\n            }, this)\n        }, index, false, {\n            fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n            lineNumber: 100,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: \"100vh\",\n            backgroundColor: \"#f8f9fa\",\n            padding: \"1rem\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                maxWidth: \"900px\",\n                margin: \"0 auto\",\n                backgroundColor: \"white\",\n                borderRadius: \"1rem\",\n                boxShadow: \"0 4px 6px rgba(0, 0, 0, 0.1)\",\n                overflow: \"hidden\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: \"1.5rem\",\n                        backgroundColor: \"#007bff\",\n                        color: \"white\",\n                        display: \"flex\",\n                        justifyContent: \"space-between\",\n                        alignItems: \"center\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        margin: 0,\n                                        fontSize: \"1.5rem\",\n                                        fontWeight: \"bold\"\n                                    },\n                                    children: \"LeanAtom Assistant\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        margin: \"0.25rem 0 0 0\",\n                                        opacity: 0.9,\n                                        fontSize: \"0.875rem\"\n                                    },\n                                    children: \"地球化学 + Lean 4 数学证明助手\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                gap: \"0.5rem\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowLeanCode(!showLeanCode),\n                                    style: {\n                                        padding: \"0.5rem 1rem\",\n                                        backgroundColor: \"rgba(255,255,255,0.2)\",\n                                        color: \"white\",\n                                        border: \"none\",\n                                        borderRadius: \"0.5rem\",\n                                        cursor: \"pointer\",\n                                        fontSize: \"0.875rem\"\n                                    },\n                                    children: showLeanCode ? \"隐藏代码\" : \"显示代码\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: clearConversation,\n                                    style: {\n                                        padding: \"0.5rem 1rem\",\n                                        backgroundColor: \"rgba(255,255,255,0.2)\",\n                                        color: \"white\",\n                                        border: \"none\",\n                                        borderRadius: \"0.5rem\",\n                                        cursor: \"pointer\",\n                                        fontSize: \"0.875rem\"\n                                    },\n                                    children: \"清空对话\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n                    lineNumber: 209,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        height: \"500px\",\n                        overflowY: \"auto\",\n                        padding: \"1rem\",\n                        backgroundColor: \"#ffffff\"\n                    },\n                    children: [\n                        messages.map((msg, i)=>renderMessage(msg, i)),\n                        loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                justifyContent: \"center\",\n                                padding: \"1rem\",\n                                color: \"#6c757d\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"\\uD83E\\uDD14 正在思考中...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n                                lineNumber: 272,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n                            lineNumber: 266,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesEndRef\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n                    lineNumber: 258,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: \"1rem\",\n                        borderTop: \"1px solid #dee2e6\",\n                        backgroundColor: \"#f8f9fa\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                gap: \"0.75rem\",\n                                alignItems: \"flex-end\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    ref: inputRef,\n                                    value: input,\n                                    onChange: (e)=>setInput(e.target.value),\n                                    onKeyDown: handleKeyDown,\n                                    placeholder: \"请输入您的地球化学或环境科学问题...\",\n                                    style: {\n                                        flex: 1,\n                                        padding: \"0.75rem\",\n                                        border: \"1px solid #ced4da\",\n                                        borderRadius: \"0.5rem\",\n                                        resize: \"vertical\",\n                                        minHeight: \"2.5rem\",\n                                        maxHeight: \"8rem\",\n                                        fontSize: \"1rem\",\n                                        fontFamily: \"inherit\"\n                                    },\n                                    rows: 1\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n                                    lineNumber: 285,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleAsk,\n                                    disabled: loading || !input.trim(),\n                                    style: {\n                                        padding: \"0.75rem 1.5rem\",\n                                        backgroundColor: loading || !input.trim() ? \"#6c757d\" : \"#007bff\",\n                                        color: \"white\",\n                                        border: \"none\",\n                                        borderRadius: \"0.5rem\",\n                                        cursor: loading || !input.trim() ? \"not-allowed\" : \"pointer\",\n                                        fontSize: \"1rem\",\n                                        fontWeight: \"500\",\n                                        minWidth: \"5rem\"\n                                    },\n                                    children: loading ? \"思考中...\" : \"发送\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n                            lineNumber: 284,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginTop: \"0.5rem\",\n                                fontSize: \"0.75rem\",\n                                color: \"#6c757d\",\n                                textAlign: \"center\"\n                            },\n                            children: \"按 Enter 发送，Shift + Enter 换行\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n                            lineNumber: 322,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n                    lineNumber: 279,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n            lineNumber: 200,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Project/LeanAtom/components/LeanGptAssistant.js\",\n        lineNumber: 195,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/LeanGptAssistant.js\n");

/***/ }),

/***/ "./pages/index.js":
/*!************************!*\
  !*** ./pages/index.js ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_LeanGptAssistant__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/LeanGptAssistant */ \"./components/LeanGptAssistant.js\");\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    children: \"LeanAtom\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Project/LeanAtom/pages/index.js\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Project/LeanAtom/pages/index.js\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LeanGptAssistant__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Project/LeanAtom/pages/index.js\",\n                    lineNumber: 11,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Project/LeanAtom/pages/index.js\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTZCO0FBQ2lDO0FBRS9DLFNBQVNFO0lBQ3RCLHFCQUNFOzswQkFDRSw4REFBQ0Ysa0RBQUlBOzBCQUNILDRFQUFDRzs4QkFBTTs7Ozs7Ozs7Ozs7MEJBRVQsOERBQUNDOzBCQUNDLDRFQUFDSCxvRUFBZ0JBOzs7Ozs7Ozs7Ozs7QUFJekIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sZWFuYXRvbS8uL3BhZ2VzL2luZGV4LmpzP2JlZTciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IEhlYWQgZnJvbSAnbmV4dC9oZWFkJztcbmltcG9ydCBMZWFuR3B0QXNzaXN0YW50IGZyb20gJy4uL2NvbXBvbmVudHMvTGVhbkdwdEFzc2lzdGFudCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhvbWUoKSB7XG4gIHJldHVybiAoXG4gICAgPD5cbiAgICAgIDxIZWFkPlxuICAgICAgICA8dGl0bGU+TGVhbkF0b208L3RpdGxlPlxuICAgICAgPC9IZWFkPlxuICAgICAgPG1haW4+XG4gICAgICAgIDxMZWFuR3B0QXNzaXN0YW50IC8+XG4gICAgICA8L21haW4+XG4gICAgPC8+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiSGVhZCIsIkxlYW5HcHRBc3Npc3RhbnQiLCJIb21lIiwidGl0bGUiLCJtYWluIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./pages/index.js\n");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();