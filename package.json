{"name": "leanatom", "version": "1.0.0", "description": "地球化学 + <PERSON><PERSON> 4 对话式证明助手", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "lean:check": "bash scripts/check-lean.sh", "lean:build": "cd lean && lake build", "lean:init": "cd lean && lake exe cache get && lake build", "test:hf": "node scripts/test-huggingface.js", "setup": "npm install && bash scripts/check-lean.sh", "deploy:vercel": "vercel --prod", "deploy:netlify": "netlify deploy --prod", "db:migrate": "node database/migrate.js", "db:setup": "node scripts/setup-database.js", "db:check": "node scripts/check-database.js", "test:deployment": "node scripts/test-deployment.js", "data:separate": "node scripts/separate-data.js", "data:import": "node scripts/import-to-database.js", "db:create": "node scripts/create-database-schema.js", "storage:health": "node -e \"require('dotenv').config({path:'.env.local'}); const {getStorageManager} = require('./lib/storage/StorageManager.js'); getStorageManager().then(m => m.healthCheck()).then(console.log).catch(console.error)\"", "storage:stats": "node -e \"require('dotenv').config({path:'.env.local'}); const {getStorageManager} = require('./lib/storage/StorageManager.js'); getStorageManager().then(m => m.getStatistics()).then(console.log).catch(console.error)\"", "deploy:import": "node scripts/deploy-import-data.js", "postbuild": "npm run db:setup"}, "dependencies": {"@supabase/supabase-js": "^2.50.2", "dotenv": "^16.5.0", "next": "14.0.4", "openai": "^4.0.0", "react": "18.2.0", "react-dom": "18.2.0"}, "devDependencies": {"@testing-library/jest-dom": "^5.0.0", "@testing-library/react": "^13.0.0", "eslint": "^8.0.0", "eslint-config-next": "14.0.4", "jest": "^29.0.0"}, "keywords": ["lean", "mathematics", "geochemistry", "formal-verification", "ai", "gpt", "environmental-science"], "author": "LeanAtom Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/leanatom.git"}, "bugs": {"url": "https://github.com/your-username/leanatom/issues"}, "homepage": "https://leanatom.vercel.app"}