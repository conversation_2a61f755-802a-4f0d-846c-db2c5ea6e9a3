{"name": "leanatom", "version": 2, "builds": [{"src": "package.json", "use": "@vercel/next"}], "routes": [{"src": "/api/(.*)", "dest": "/api/$1"}, {"src": "/(.*)", "dest": "/$1"}], "env": {"LLM_PROVIDER": "@llm-provider", "HUGGINGFACE_API_KEY": "@huggingface-api-key", "OPENAI_API_KEY": "@openai-api-key", "ENABLE_LEAN_VERIFICATION": "@enable-lean-verification"}, "functions": {"pages/api/lean-gpt.js": {"maxDuration": 30}, "pages/api/verify-lean.js": {"maxDuration": 30}}}