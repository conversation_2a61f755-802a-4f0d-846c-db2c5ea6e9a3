import { useState, useEffect } from 'react';

export default function QuestionBankSidebar({
  selectedCategory,
  expanded,
  onCategorySelect,
  onQuestionSelect,
  onClose
}) {
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [statistics, setStatistics] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchTags, setSearchTags] = useState('');
  const [expandedTags, setExpandedTags] = useState({});

  // 加载分类数据
  useEffect(() => {
    loadCategories();
    loadStatistics();
  }, []);

  const loadCategories = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/question-bank?action=categories');
      const data = await response.json();
      setCategories(data.categories || []);
    } catch (error) {
      console.error('加载分类失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadStatistics = async () => {
    try {
      const response = await fetch('/api/question-bank?action=statistics');
      const data = await response.json();
      setStatistics(data.statistics);
    } catch (error) {
      console.error('加载统计信息失败:', error);
    }
  };

  // 获取难度颜色
  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'easy': return '#38a169';
      case 'medium': return '#d69e2e';
      case 'hard': return '#e53e3e';
      default: return '#718096';
    }
  };

  // 获取难度文本和图标
  const getDifficultyInfo = (difficulty) => {
    switch (difficulty) {
      case 'easy': return { text: '简单', icon: '🟢' };
      case 'medium': return { text: '中等', icon: '🟡' };
      case 'hard': return { text: '困难', icon: '🔴' };
      default: return { text: '未知', icon: '⚪' };
    }
  };

  // 美化数字显示
  const formatNumber = (num, difficulty = null) => {
    let backgroundColor = '#3182ce';
    if (difficulty === 'easy') backgroundColor = '#38a169';
    else if (difficulty === 'medium') backgroundColor = '#d69e2e';
    else if (difficulty === 'hard') backgroundColor = '#e53e3e';

    return (
      <span style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        minWidth: '24px',
        height: '20px',
        backgroundColor,
        color: 'white',
        borderRadius: '10px',
        fontSize: '0.75rem',
        fontWeight: '600',
        padding: '0 6px'
      }}>
        {num}
      </span>
    );
  };

  // 过滤问题
  const filterQuestions = (questions) => {
    if (!searchQuery && !searchTags) return questions;

    return questions.filter(question => {
      const matchesQuery = !searchQuery ||
        question.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        question.content.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesTags = !searchTags ||
        question.tags.some(tag =>
          tag.toLowerCase().includes(searchTags.toLowerCase())
        );

      return matchesQuery && matchesTags;
    });
  };

  const sidebarWidth = expanded ? '600px' : '280px';

  if (loading) {
    return (
      <div style={{
        width: sidebarWidth,
        backgroundColor: '#f8f9fa',
        borderLeft: '1px solid #e1e5e9',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        transition: 'width 0.3s ease'
      }}>
        <div>加载中...</div>
      </div>
    );
  }

  return (
    <div style={{
      width: sidebarWidth,
      backgroundColor: '#f8f9fa',
      borderLeft: '1px solid #e1e5e9',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden',
      transition: 'width 0.3s ease'
    }}>
      {/* 头部 */}
      <div style={{
        padding: '1.5rem',
        borderBottom: '1px solid #e1e5e9',
        backgroundColor: 'white'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '1rem'
        }}>
          <h2 style={{
            margin: 0,
            fontSize: expanded && selectedCategory ? '1.5rem' : '1.25rem',
            fontWeight: '700',
            color: expanded && selectedCategory ? '#1a365d' : '#2d3748',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            textShadow: expanded && selectedCategory ? '0 1px 2px rgba(0,0,0,0.1)' : 'none'
          }}>
            {expanded && selectedCategory ? (
              <>
                📁 {selectedCategory.name} {formatNumber(selectedCategory.questions.length)}
              </>
            ) : (
              <>
                📚 问题库
              </>
            )}
          </h2>
          {expanded && (
            <button
              onClick={onClose}
              style={{
                background: 'none',
                border: 'none',
                borderRadius: '50%',
                width: '40px',
                height: '40px',
                cursor: 'pointer',
                color: '#718096',
                fontSize: '1.5rem',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                transition: 'all 0.2s'
              }}
              onMouseOver={(e) => {
                e.target.style.color = '#2d3748';
                e.target.style.transform = 'scale(1.1)';
              }}
              onMouseOut={(e) => {
                e.target.style.color = '#718096';
                e.target.style.transform = 'scale(1)';
              }}
              title="返回分类列表"
            >
              ⬅️
            </button>
          )}
        </div>

        {/* 统计信息 */}
        {!expanded && statistics && (
          <div style={{
            fontSize: '0.875rem',
            color: '#4a5568',
            display: 'flex',
            flexDirection: 'column',
            gap: '0.5rem'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
              <span>📁 分类:</span>
              {formatNumber(statistics.totalCategories)}
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
              <span>❓ 问题:</span>
              {formatNumber(statistics.totalQuestions)}
            </div>
            <div style={{
              display: 'flex',
              gap: '0.75rem',
              marginTop: '0.25rem',
              fontSize: '0.75rem',
              flexWrap: 'wrap'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                <span>简单</span>
                {formatNumber(statistics.questionsByDifficulty.easy, 'easy')}
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                <span>中等</span>
                {formatNumber(statistics.questionsByDifficulty.medium, 'medium')}
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                <span>困难</span>
                {formatNumber(statistics.questionsByDifficulty.hard, 'hard')}
              </div>
            </div>
          </div>
        )}

        {/* 分类描述 */}
        {expanded && selectedCategory && (
          <p style={{
            margin: 0,
            fontSize: '0.875rem',
            color: '#718096',
            lineHeight: '1.5'
          }}>
            {selectedCategory.description}
          </p>
        )}
      </div>

      {/* 内容区域 */}
      <div style={{
        flex: 1,
        overflow: 'auto',
        padding: '1rem'
      }}>
        {!expanded ? (
          // 显示分类列表
          <div>
            {categories.map((category) => (
              <div
                key={category.id}
                onClick={() => onCategorySelect(category)}
                style={{
                  padding: '1rem',
                  margin: '0.5rem 0',
                  backgroundColor: 'white',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  border: '1px solid #e2e8f0',
                  transition: 'all 0.2s'
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.backgroundColor = '#f7fafc';
                  e.currentTarget.style.transform = 'translateY(-1px)';
                  e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
                  // 箭头动画
                  const arrow = e.currentTarget.querySelector('[data-arrow]');
                  if (arrow) {
                    arrow.style.transform = 'translateX(4px)';
                    arrow.style.color = '#2b6cb0';
                  }
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.backgroundColor = 'white';
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = 'none';
                  // 箭头动画
                  const arrow = e.currentTarget.querySelector('[data-arrow]');
                  if (arrow) {
                    arrow.style.transform = 'translateX(0)';
                    arrow.style.color = '#3182ce';
                  }
                }}
              >
                <div style={{
                  fontSize: '1rem',
                  fontWeight: '600',
                  color: '#2d3748',
                  marginBottom: '0.5rem',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem'
                }}>
                  📁 {category.name} {formatNumber(category.questions.length)}
                </div>
                <div style={{
                  fontSize: '0.875rem',
                  color: '#718096',
                  marginBottom: '0.5rem',
                  lineHeight: '1.4'
                }}>
                  {category.description}
                </div>
                <div style={{
                  fontSize: '0.75rem',
                  color: '#4a5568',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                    <span>❓</span>
                    {formatNumber(category.questions.length)}
                  </div>
                  <div
                    data-arrow
                    style={{
                      fontSize: '1.5rem',
                      color: '#3182ce',
                      transform: 'translateX(0)',
                      transition: 'all 0.2s ease'
                    }}
                  >
                    ➤
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          // 显示问题列表
          selectedCategory && (
            <div>
              {/* 搜索栏 */}
              <div style={{ marginBottom: '1rem' }}>
                <input
                  type="text"
                  placeholder="搜索问题..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #e2e8f0',
                    borderRadius: '8px',
                    fontSize: '0.875rem',
                    marginBottom: '0.5rem'
                  }}
                />
                <input
                  type="text"
                  placeholder="搜索标签..."
                  value={searchTags}
                  onChange={(e) => setSearchTags(e.target.value)}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #e2e8f0',
                    borderRadius: '8px',
                    fontSize: '0.875rem'
                  }}
                />
              </div>
              {filterQuestions(selectedCategory.questions).map((question) => (
                <div
                  key={question.id}
                  onClick={() => onQuestionSelect(question.content)}
                  style={{
                    backgroundColor: 'white',
                    border: '1px solid #e2e8f0',
                    borderRadius: '8px',
                    padding: '1rem',
                    marginBottom: '1rem',
                    cursor: 'pointer',
                    transition: 'all 0.2s'
                  }}
                  onMouseOver={(e) => {
                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
                    e.currentTarget.style.transform = 'translateY(-2px)';
                  }}
                  onMouseOut={(e) => {
                    e.currentTarget.style.boxShadow = 'none';
                    e.currentTarget.style.transform = 'translateY(0)';
                  }}
                >
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start',
                    marginBottom: '0.5rem'
                  }}>
                    <h4 style={{
                      margin: 0,
                      fontSize: '1rem',
                      fontWeight: '600',
                      color: '#2d3748',
                      flex: 1,
                      lineHeight: '1.4'
                    }}>
                      {question.title}
                    </h4>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.25rem',
                      padding: '0.25rem 0.5rem',
                      fontSize: '0.75rem',
                      backgroundColor: getDifficultyColor(question.difficulty),
                      color: 'white',
                      borderRadius: '12px',
                      fontWeight: '500',
                      marginLeft: '0.5rem',
                      flexShrink: 0
                    }}>
                      {getDifficultyInfo(question.difficulty).icon}
                      {getDifficultyInfo(question.difficulty).text}
                    </div>
                  </div>

                  <p style={{
                    margin: '0 0 0.75rem 0',
                    fontSize: '0.875rem',
                    color: '#4a5568',
                    lineHeight: '1.5',
                    display: '-webkit-box',
                    WebkitLineClamp: 3,
                    WebkitBoxOrient: 'vertical',
                    overflow: 'hidden'
                  }}>
                    {question.content}
                  </p>

                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    fontSize: '0.75rem',
                    color: '#718096'
                  }}>
                    <div style={{
                      display: 'flex',
                      gap: '0.5rem',
                      flexWrap: 'wrap',
                      alignItems: 'center'
                    }}>
                      {(expandedTags[question.id] ? question.tags : question.tags.slice(0, 3)).map((tag, index) => (
                        <span
                          key={index}
                          style={{
                            padding: '0.125rem 0.375rem',
                            backgroundColor: '#edf2f7',
                            borderRadius: '4px',
                            fontSize: '0.75rem'
                          }}
                        >
                          {tag}
                        </span>
                      ))}
                      {question.tags.length > 3 && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setExpandedTags(prev => ({
                              ...prev,
                              [question.id]: !prev[question.id]
                            }));
                          }}
                          style={{
                            background: 'none',
                            border: 'none',
                            color: '#3182ce',
                            cursor: 'pointer',
                            fontSize: '0.75rem',
                            padding: '0.125rem 0.25rem'
                          }}
                        >
                          {expandedTags[question.id] ? '收起' : `+${question.tags.length - 3}`}
                        </button>
                      )}
                    </div>
                    <span>{question.createdAt}</span>
                  </div>
                </div>
              ))}
            </div>
          )
        )}
      </div>
    </div>
  );
}
