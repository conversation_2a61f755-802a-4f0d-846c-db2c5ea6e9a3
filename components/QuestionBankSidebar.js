import { useState, useEffect } from 'react';

export default function QuestionBankSidebar({ 
  selectedCategory, 
  expanded, 
  onCategorySelect, 
  onQuestionSelect, 
  onClose 
}) {
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [statistics, setStatistics] = useState(null);

  // 加载分类数据
  useEffect(() => {
    loadCategories();
    loadStatistics();
  }, []);

  const loadCategories = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/question-bank?action=categories');
      const data = await response.json();
      setCategories(data.categories || []);
    } catch (error) {
      console.error('加载分类失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadStatistics = async () => {
    try {
      const response = await fetch('/api/question-bank?action=statistics');
      const data = await response.json();
      setStatistics(data.statistics);
    } catch (error) {
      console.error('加载统计信息失败:', error);
    }
  };

  // 获取难度颜色
  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'easy': return '#38a169';
      case 'medium': return '#d69e2e';
      case 'hard': return '#e53e3e';
      default: return '#718096';
    }
  };

  // 获取难度文本和图标
  const getDifficultyInfo = (difficulty) => {
    switch (difficulty) {
      case 'easy': return { text: '简单', icon: '🟢' };
      case 'medium': return { text: '中等', icon: '🟡' };
      case 'hard': return { text: '困难', icon: '🔴' };
      default: return { text: '未知', icon: '⚪' };
    }
  };

  // 美化数字显示
  const formatNumber = (num) => {
    return (
      <span style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        minWidth: '24px',
        height: '20px',
        backgroundColor: '#3182ce',
        color: 'white',
        borderRadius: '10px',
        fontSize: '0.75rem',
        fontWeight: '600',
        padding: '0 6px'
      }}>
        {num}
      </span>
    );
  };

  const sidebarWidth = expanded ? '600px' : '280px';

  if (loading) {
    return (
      <div style={{
        width: sidebarWidth,
        backgroundColor: '#f8f9fa',
        borderLeft: '1px solid #e1e5e9',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        transition: 'width 0.3s ease'
      }}>
        <div>加载中...</div>
      </div>
    );
  }

  return (
    <div style={{
      width: sidebarWidth,
      backgroundColor: '#f8f9fa',
      borderLeft: '1px solid #e1e5e9',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden',
      transition: 'width 0.3s ease'
    }}>
      {/* 头部 */}
      <div style={{
        padding: '1.5rem',
        borderBottom: '1px solid #e1e5e9',
        backgroundColor: 'white'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '1rem'
        }}>
          <h2 style={{
            margin: 0,
            fontSize: expanded && selectedCategory ? '1.5rem' : '1.25rem',
            fontWeight: '700',
            color: expanded && selectedCategory ? '#1a365d' : '#2d3748',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            textShadow: expanded && selectedCategory ? '0 1px 2px rgba(0,0,0,0.1)' : 'none'
          }}>
            {expanded && selectedCategory ? (
              <>
                📁 {selectedCategory.name}
              </>
            ) : (
              <>
                📚 问题库
              </>
            )}
          </h2>
          {expanded && (
            <button
              onClick={onClose}
              style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                border: 'none',
                borderRadius: '50%',
                width: '40px',
                height: '40px',
                cursor: 'pointer',
                color: 'white',
                fontSize: '1.2rem',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                transition: 'all 0.2s'
              }}
              onMouseOver={(e) => {
                e.target.style.transform = 'scale(1.1)';
                e.target.style.boxShadow = '0 4px 12px rgba(0,0,0,0.2)';
              }}
              onMouseOut={(e) => {
                e.target.style.transform = 'scale(1)';
                e.target.style.boxShadow = '0 2px 8px rgba(0,0,0,0.15)';
              }}
              title="返回分类列表"
            >
              ⬅️
            </button>
          )}
        </div>

        {/* 统计信息 */}
        {!expanded && statistics && (
          <div style={{
            fontSize: '0.875rem',
            color: '#4a5568',
            display: 'flex',
            flexDirection: 'column',
            gap: '0.5rem'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
              <span>📁 分类:</span>
              {formatNumber(statistics.totalCategories)}
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
              <span>❓ 问题:</span>
              {formatNumber(statistics.totalQuestions)}
            </div>
            <div style={{
              display: 'flex',
              gap: '0.75rem',
              marginTop: '0.25rem',
              fontSize: '0.75rem',
              flexWrap: 'wrap'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                🟢 {formatNumber(statistics.questionsByDifficulty.easy)}
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                🟡 {formatNumber(statistics.questionsByDifficulty.medium)}
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                🔴 {formatNumber(statistics.questionsByDifficulty.hard)}
              </div>
            </div>
          </div>
        )}

        {/* 分类描述 */}
        {expanded && selectedCategory && (
          <p style={{
            margin: 0,
            fontSize: '0.875rem',
            color: '#718096',
            lineHeight: '1.5'
          }}>
            {selectedCategory.description}
          </p>
        )}
      </div>

      {/* 内容区域 */}
      <div style={{
        flex: 1,
        overflow: 'auto',
        padding: '1rem'
      }}>
        {!expanded ? (
          // 显示分类列表
          <div>
            {categories.map((category) => (
              <div
                key={category.id}
                onClick={() => onCategorySelect(category)}
                style={{
                  padding: '1rem',
                  margin: '0.5rem 0',
                  backgroundColor: 'white',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  border: '1px solid #e2e8f0',
                  transition: 'all 0.2s'
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.backgroundColor = '#f7fafc';
                  e.currentTarget.style.transform = 'translateY(-1px)';
                  e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
                  // 箭头动画
                  const arrow = e.currentTarget.querySelector('[data-arrow]');
                  if (arrow) {
                    arrow.style.transform = 'translateX(4px)';
                    arrow.style.color = '#2b6cb0';
                  }
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.backgroundColor = 'white';
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = 'none';
                  // 箭头动画
                  const arrow = e.currentTarget.querySelector('[data-arrow]');
                  if (arrow) {
                    arrow.style.transform = 'translateX(0)';
                    arrow.style.color = '#3182ce';
                  }
                }}
              >
                <div style={{
                  fontSize: '1rem',
                  fontWeight: '600',
                  color: '#2d3748',
                  marginBottom: '0.5rem',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem'
                }}>
                  📁 {category.name}
                </div>
                <div style={{
                  fontSize: '0.875rem',
                  color: '#718096',
                  marginBottom: '0.5rem',
                  lineHeight: '1.4'
                }}>
                  {category.description}
                </div>
                <div style={{
                  fontSize: '0.75rem',
                  color: '#4a5568',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                    <span>❓</span>
                    {formatNumber(category.questions.length)}
                  </div>
                  <div
                    data-arrow
                    style={{
                      fontSize: '1.5rem',
                      color: '#3182ce',
                      transform: 'translateX(0)',
                      transition: 'all 0.2s ease'
                    }}
                  >
                    ➤
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          // 显示问题列表
          selectedCategory && (
            <div>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                marginBottom: '1rem',
                padding: '0.75rem',
                backgroundColor: 'white',
                borderRadius: '8px',
                border: '1px solid #e2e8f0'
              }}>
                <span style={{ fontSize: '1.2rem' }}>❓</span>
                <span style={{
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  color: '#4a5568'
                }}>
                  问题列表
                </span>
                {formatNumber(selectedCategory.questions.length)}
              </div>
              {selectedCategory.questions.map((question) => (
                <div
                  key={question.id}
                  onClick={() => onQuestionSelect(question.content)}
                  style={{
                    backgroundColor: 'white',
                    border: '1px solid #e2e8f0',
                    borderRadius: '8px',
                    padding: '1rem',
                    marginBottom: '1rem',
                    cursor: 'pointer',
                    transition: 'all 0.2s'
                  }}
                  onMouseOver={(e) => {
                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
                    e.currentTarget.style.transform = 'translateY(-2px)';
                  }}
                  onMouseOut={(e) => {
                    e.currentTarget.style.boxShadow = 'none';
                    e.currentTarget.style.transform = 'translateY(0)';
                  }}
                >
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start',
                    marginBottom: '0.5rem'
                  }}>
                    <h4 style={{
                      margin: 0,
                      fontSize: '1rem',
                      fontWeight: '600',
                      color: '#2d3748',
                      flex: 1,
                      lineHeight: '1.4'
                    }}>
                      {question.title}
                    </h4>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.25rem',
                      padding: '0.25rem 0.5rem',
                      fontSize: '0.75rem',
                      backgroundColor: getDifficultyColor(question.difficulty),
                      color: 'white',
                      borderRadius: '12px',
                      fontWeight: '500',
                      marginLeft: '0.5rem',
                      flexShrink: 0
                    }}>
                      {getDifficultyInfo(question.difficulty).icon}
                      {getDifficultyInfo(question.difficulty).text}
                    </div>
                  </div>

                  <p style={{
                    margin: '0 0 0.75rem 0',
                    fontSize: '0.875rem',
                    color: '#4a5568',
                    lineHeight: '1.5',
                    display: '-webkit-box',
                    WebkitLineClamp: 3,
                    WebkitBoxOrient: 'vertical',
                    overflow: 'hidden'
                  }}>
                    {question.content}
                  </p>

                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    fontSize: '0.75rem',
                    color: '#718096'
                  }}>
                    <div style={{
                      display: 'flex',
                      gap: '0.5rem',
                      flexWrap: 'wrap'
                    }}>
                      {question.tags.slice(0, 3).map((tag, index) => (
                        <span
                          key={index}
                          style={{
                            padding: '0.125rem 0.375rem',
                            backgroundColor: '#edf2f7',
                            borderRadius: '4px',
                            fontSize: '0.75rem'
                          }}
                        >
                          {tag}
                        </span>
                      ))}
                      {question.tags.length > 3 && (
                        <span style={{ color: '#718096' }}>+{question.tags.length - 3}</span>
                      )}
                    </div>
                    <span>{question.createdAt}</span>
                  </div>
                </div>
              ))}
            </div>
          )
        )}
      </div>
    </div>
  );
}
