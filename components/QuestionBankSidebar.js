import { useState, useEffect } from 'react';

export default function QuestionBankSidebar({ 
  selectedCategory, 
  expanded, 
  onCategorySelect, 
  onQuestionSelect, 
  onClose 
}) {
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [statistics, setStatistics] = useState(null);

  // 加载分类数据
  useEffect(() => {
    loadCategories();
    loadStatistics();
  }, []);

  const loadCategories = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/question-bank?action=categories');
      const data = await response.json();
      setCategories(data.categories || []);
    } catch (error) {
      console.error('加载分类失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadStatistics = async () => {
    try {
      const response = await fetch('/api/question-bank?action=statistics');
      const data = await response.json();
      setStatistics(data.statistics);
    } catch (error) {
      console.error('加载统计信息失败:', error);
    }
  };

  // 获取难度颜色
  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'easy': return '#38a169';
      case 'medium': return '#d69e2e';
      case 'hard': return '#e53e3e';
      default: return '#718096';
    }
  };

  // 获取难度文本
  const getDifficultyText = (difficulty) => {
    switch (difficulty) {
      case 'easy': return '简单';
      case 'medium': return '中等';
      case 'hard': return '困难';
      default: return '未知';
    }
  };

  const sidebarWidth = expanded ? '600px' : '280px';

  if (loading) {
    return (
      <div style={{
        width: sidebarWidth,
        backgroundColor: '#f8f9fa',
        borderLeft: '1px solid #e1e5e9',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        transition: 'width 0.3s ease'
      }}>
        <div>加载中...</div>
      </div>
    );
  }

  return (
    <div style={{
      width: sidebarWidth,
      backgroundColor: '#f8f9fa',
      borderLeft: '1px solid #e1e5e9',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden',
      transition: 'width 0.3s ease'
    }}>
      {/* 头部 */}
      <div style={{
        padding: '1.5rem',
        borderBottom: '1px solid #e1e5e9',
        backgroundColor: 'white'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '1rem'
        }}>
          <h2 style={{
            margin: 0,
            fontSize: '1.25rem',
            fontWeight: '600',
            color: '#2d3748'
          }}>
            {expanded && selectedCategory ? selectedCategory.name : '问题库'}
          </h2>
          {expanded && (
            <button
              onClick={onClose}
              style={{
                background: 'none',
                border: 'none',
                fontSize: '1.5rem',
                cursor: 'pointer',
                color: '#718096'
              }}
            >
              ←
            </button>
          )}
        </div>

        {/* 统计信息 */}
        {!expanded && statistics && (
          <div style={{
            fontSize: '0.875rem',
            color: '#4a5568'
          }}>
            <div>分类: {statistics.totalCategories}</div>
            <div>问题: {statistics.totalQuestions}</div>
            <div style={{ display: 'flex', gap: '0.5rem', marginTop: '0.25rem', fontSize: '0.75rem' }}>
              <span>简单: {statistics.questionsByDifficulty.easy}</span>
              <span>中等: {statistics.questionsByDifficulty.medium}</span>
              <span>困难: {statistics.questionsByDifficulty.hard}</span>
            </div>
          </div>
        )}

        {/* 分类描述 */}
        {expanded && selectedCategory && (
          <p style={{
            margin: 0,
            fontSize: '0.875rem',
            color: '#718096',
            lineHeight: '1.5'
          }}>
            {selectedCategory.description}
          </p>
        )}
      </div>

      {/* 内容区域 */}
      <div style={{
        flex: 1,
        overflow: 'auto',
        padding: '1rem'
      }}>
        {!expanded ? (
          // 显示分类列表
          <div>
            <h3 style={{
              fontSize: '0.875rem',
              fontWeight: '600',
              color: '#4a5568',
              margin: '0 0 1rem 0'
            }}>
              选择分类
            </h3>
            {categories.map((category) => (
              <div
                key={category.id}
                onClick={() => onCategorySelect(category)}
                style={{
                  padding: '1rem',
                  margin: '0.5rem 0',
                  backgroundColor: 'white',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  border: '1px solid #e2e8f0',
                  transition: 'all 0.2s'
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.backgroundColor = '#f7fafc';
                  e.currentTarget.style.transform = 'translateY(-1px)';
                  e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.backgroundColor = 'white';
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = 'none';
                }}
              >
                <div style={{
                  fontSize: '1rem',
                  fontWeight: '500',
                  color: '#2d3748',
                  marginBottom: '0.5rem'
                }}>
                  {category.name}
                </div>
                <div style={{
                  fontSize: '0.875rem',
                  color: '#718096',
                  marginBottom: '0.5rem',
                  lineHeight: '1.4'
                }}>
                  {category.description}
                </div>
                <div style={{
                  fontSize: '0.75rem',
                  color: '#4a5568',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}>
                  <span>{category.questions.length} 个问题</span>
                  <span style={{ color: '#3182ce' }}>点击查看 →</span>
                </div>
              </div>
            ))}
          </div>
        ) : (
          // 显示问题列表
          selectedCategory && (
            <div>
              <h3 style={{
                fontSize: '0.875rem',
                fontWeight: '600',
                color: '#4a5568',
                margin: '0 0 1rem 0'
              }}>
                问题列表 ({selectedCategory.questions.length})
              </h3>
              {selectedCategory.questions.map((question) => (
                <div
                  key={question.id}
                  onClick={() => onQuestionSelect(question.content)}
                  style={{
                    backgroundColor: 'white',
                    border: '1px solid #e2e8f0',
                    borderRadius: '8px',
                    padding: '1rem',
                    marginBottom: '1rem',
                    cursor: 'pointer',
                    transition: 'all 0.2s'
                  }}
                  onMouseOver={(e) => {
                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
                    e.currentTarget.style.transform = 'translateY(-2px)';
                  }}
                  onMouseOut={(e) => {
                    e.currentTarget.style.boxShadow = 'none';
                    e.currentTarget.style.transform = 'translateY(0)';
                  }}
                >
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start',
                    marginBottom: '0.5rem'
                  }}>
                    <h4 style={{
                      margin: 0,
                      fontSize: '1rem',
                      fontWeight: '600',
                      color: '#2d3748',
                      flex: 1,
                      lineHeight: '1.4'
                    }}>
                      {question.title}
                    </h4>
                    <span style={{
                      padding: '0.25rem 0.5rem',
                      fontSize: '0.75rem',
                      backgroundColor: getDifficultyColor(question.difficulty),
                      color: 'white',
                      borderRadius: '12px',
                      fontWeight: '500',
                      marginLeft: '0.5rem',
                      flexShrink: 0
                    }}>
                      {getDifficultyText(question.difficulty)}
                    </span>
                  </div>

                  <p style={{
                    margin: '0 0 0.75rem 0',
                    fontSize: '0.875rem',
                    color: '#4a5568',
                    lineHeight: '1.5',
                    display: '-webkit-box',
                    WebkitLineClamp: 3,
                    WebkitBoxOrient: 'vertical',
                    overflow: 'hidden'
                  }}>
                    {question.content}
                  </p>

                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    fontSize: '0.75rem',
                    color: '#718096'
                  }}>
                    <div style={{
                      display: 'flex',
                      gap: '0.5rem',
                      flexWrap: 'wrap'
                    }}>
                      {question.tags.slice(0, 3).map((tag, index) => (
                        <span
                          key={index}
                          style={{
                            padding: '0.125rem 0.375rem',
                            backgroundColor: '#edf2f7',
                            borderRadius: '4px',
                            fontSize: '0.75rem'
                          }}
                        >
                          {tag}
                        </span>
                      ))}
                      {question.tags.length > 3 && (
                        <span style={{ color: '#718096' }}>+{question.tags.length - 3}</span>
                      )}
                    </div>
                    <span>{question.createdAt}</span>
                  </div>
                </div>
              ))}
            </div>
          )
        )}
      </div>
    </div>
  );
}
