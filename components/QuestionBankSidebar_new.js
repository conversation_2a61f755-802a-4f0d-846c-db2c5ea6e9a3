import { useState, useEffect } from 'react';

export default function QuestionBankSidebar({ 
  expanded, 
  onCategorySelect, 
  onQuestionSelect, 
  onClose, 
  selectedCategory 
}) {
  const [categories, setCategories] = useState([]);
  const [statistics, setStatistics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTags, setSelectedTags] = useState([]);
  const [expandedTags, setExpandedTags] = useState({});
  const [showAllTags, setShowAllTags] = useState(false);
  const [allTags, setAllTags] = useState([]);
  const [showAddCategory, setShowAddCategory] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [editingQuestion, setEditingQuestion] = useState(null);

  const sidebarWidth = expanded ? '600px' : '280px';

  // 加载分类数据
  useEffect(() => {
    loadCategories();
    loadStatistics();
  }, []);

  const loadCategories = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/question-bank?action=categories');
      const data = await response.json();
      setCategories(data.categories || []);
      
      // 提取所有标签
      const tags = new Set();
      data.categories?.forEach(category => {
        category.questions?.forEach(question => {
          question.tags?.forEach(tag => tags.add(tag));
        });
      });
      setAllTags(Array.from(tags));
    } catch (error) {
      console.error('加载分类失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadStatistics = async () => {
    try {
      const response = await fetch('/api/question-bank?action=statistics');
      const data = await response.json();
      setStatistics(data.statistics);
    } catch (error) {
      console.error('加载统计信息失败:', error);
    }
  };

  // 搜索所有问题
  const searchAllQuestions = () => {
    const results = [];
    const query = searchQuery.toLowerCase();
    
    categories.forEach(category => {
      const matchingQuestions = category.questions.filter(question => {
        const titleMatch = question.title.toLowerCase().includes(query);
        const contentMatch = question.content.toLowerCase().includes(query);
        const tagMatch = selectedTags.length === 0 || selectedTags.every(tag => 
          question.tags && question.tags.includes(tag)
        );
        
        return (titleMatch || contentMatch) && tagMatch;
      });
      
      if (matchingQuestions.length > 0) {
        results.push({
          category: category.name,
          categoryId: category.id,
          questions: matchingQuestions
        });
      }
    });
    
    return results;
  };

  // 获取难度信息
  const getDifficultyInfo = (difficulty) => {
    switch (difficulty) {
      case 'easy': return { text: '简单', icon: '🟢' };
      case 'medium': return { text: '中等', icon: '🟡' };
      case 'hard': return { text: '困难', icon: '🔴' };
      default: return { text: '未知', icon: '⚪' };
    }
  };

  // 获取难度颜色
  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'easy': return '#38a169';
      case 'medium': return '#d69e2e';
      case 'hard': return '#e53e3e';
      default: return '#718096';
    }
  };

  // 处理标签选择
  const handleTagSelect = (tag) => {
    setSelectedTags(prev => {
      if (prev.includes(tag)) {
        return prev.filter(t => t !== tag);
      } else {
        return [...prev, tag];
      }
    });
  };

  // 添加分类
  const handleAddCategory = async (categoryData) => {
    try {
      const response = await fetch('/api/question-bank', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'addCategory',
          category: categoryData
        })
      });
      
      if (response.ok) {
        loadCategories();
        setShowAddCategory(false);
      }
    } catch (error) {
      console.error('添加分类失败:', error);
    }
  };

  // 编辑分类
  const handleEditCategory = async (categoryData) => {
    try {
      const response = await fetch('/api/question-bank', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'updateCategory',
          categoryId: editingCategory.id,
          category: categoryData
        })
      });
      
      if (response.ok) {
        loadCategories();
        setEditingCategory(null);
      }
    } catch (error) {
      console.error('编辑分类失败:', error);
    }
  };

  // 删除分类
  const handleDeleteCategory = async (category) => {
    if (category.questions.length > 0) {
      alert(`无法删除分组"${category.name}"，请先删除该分组中的所有问题（共${category.questions.length}个问题）。`);
      return;
    }

    if (confirm(`确定要删除分组"${category.name}"吗？`)) {
      try {
        const response = await fetch('/api/question-bank', {
          method: 'DELETE',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'deleteCategory',
            categoryId: category.id
          })
        });
        
        if (response.ok) {
          loadCategories();
        }
      } catch (error) {
        console.error('删除分类失败:', error);
      }
    }
  };

  // 编辑问题
  const handleEditQuestion = async (questionData) => {
    try {
      const response = await fetch('/api/question-bank', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'updateQuestion',
          questionId: editingQuestion.id,
          question: questionData
        })
      });
      
      if (response.ok) {
        loadCategories();
        setEditingQuestion(null);
      }
    } catch (error) {
      console.error('编辑问题失败:', error);
    }
  };

  // 删除问题
  const handleDeleteQuestion = async (question) => {
    if (confirm(`确定要删除问题"${question.title}"吗？`)) {
      try {
        const response = await fetch('/api/question-bank', {
          method: 'DELETE',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'deleteQuestion',
            questionId: question.id
          })
        });
        
        if (response.ok) {
          loadCategories();
        }
      } catch (error) {
        console.error('删除问题失败:', error);
      }
    }
  };

  if (loading) {
    return (
      <div style={{
        width: sidebarWidth,
        backgroundColor: '#f8f9fa',
        borderLeft: '1px solid #e1e5e9',
        padding: '1rem',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div>加载中...</div>
      </div>
    );
  }

  return (
    <div style={{
      width: sidebarWidth,
      backgroundColor: '#f8f9fa',
      borderLeft: '1px solid #e1e5e9',
      height: '100vh',
      overflow: 'hidden',
      display: 'flex',
      flexDirection: 'column',
      transition: 'width 0.3s ease'
    }}>
      <div style={{
        padding: '1rem',
        borderBottom: '1px solid #e1e5e9',
        backgroundColor: 'white'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: '1rem'
        }}>
          <h3 style={{
            margin: 0,
            fontSize: '1.125rem',
            fontWeight: '700',
            color: '#2d3748'
          }}>
            📚 问题库
          </h3>
          {selectedCategory && (
            <button
              onClick={() => onCategorySelect(null)}
              style={{
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                fontSize: '1.5rem',
                color: '#3182ce',
                padding: '0.25rem'
              }}
              title="返回分类列表"
            >
              ⬅️
            </button>
          )}
        </div>
        
        {statistics && (
          <div style={{
            fontSize: '0.875rem',
            color: '#718096',
            marginBottom: '1rem'
          }}>
            共 {statistics.totalCategories} 个分组，{statistics.totalQuestions} 个问题
          </div>
        )}
      </div>

      <div style={{
        flex: 1,
        overflow: 'auto',
        padding: '1rem'
      }}>
        {!expanded ? (
          // 显示搜索和分类列表
          <div>
            {/* 搜索框 */}
            <div style={{ marginBottom: '1rem' }}>
              <input
                type="text"
                placeholder="🔍 搜索问题..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '0.875rem'
                }}
              />
            </div>

            {/* 标签展示栏 */}
            {allTags.length > 0 && (
              <div style={{ marginBottom: '1rem' }}>
                <div style={{
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  color: '#4a5568',
                  marginBottom: '0.5rem'
                }}>
                  标签筛选
                </div>
                <div style={{
                  display: 'flex',
                  flexWrap: 'wrap',
                  gap: '0.5rem',
                  maxHeight: showAllTags ? 'none' : '4rem',
                  overflow: 'hidden'
                }}>
                  {allTags.map((tag, index) => (
                    <button
                      key={index}
                      onClick={() => handleTagSelect(tag)}
                      style={{
                        padding: '0.25rem 0.5rem',
                        fontSize: '0.75rem',
                        border: '1px solid #e2e8f0',
                        borderRadius: '12px',
                        backgroundColor: selectedTags.includes(tag) ? '#3182ce' : 'white',
                        color: selectedTags.includes(tag) ? 'white' : '#4a5568',
                        cursor: 'pointer',
                        transition: 'all 0.2s'
                      }}
                    >
                      {tag}
                    </button>
                  ))}
                </div>
                {allTags.length > 10 && (
                  <button
                    onClick={() => setShowAllTags(!showAllTags)}
                    style={{
                      marginTop: '0.5rem',
                      background: 'none',
                      border: 'none',
                      color: '#3182ce',
                      fontSize: '0.75rem',
                      cursor: 'pointer'
                    }}
                  >
                    {showAllTags ? '收起' : '显示全部标签'}
                  </button>
                )}
              </div>
            )}

            {/* 搜索结果或分类列表 */}
            {(searchQuery || selectedTags.length > 0) ? (
              // 显示搜索结果
              <div>
                {searchAllQuestions().map((result, index) => (
                  <div key={index} style={{ marginBottom: '1.5rem' }}>
                    <h4 style={{
                      fontSize: '1rem',
                      fontWeight: '600',
                      color: '#2d3748',
                      marginBottom: '0.5rem'
                    }}>
                      📂 {result.category} ({result.questions.length})
                    </h4>
                    {result.questions.map((question) => (
                      <div
                        key={question.id}
                        onClick={() => onQuestionSelect(question.content)}
                        style={{
                          backgroundColor: 'white',
                          border: '1px solid #e2e8f0',
                          borderRadius: '6px',
                          padding: '0.75rem',
                          marginBottom: '0.5rem',
                          cursor: 'pointer',
                          fontSize: '0.875rem'
                        }}
                      >
                        {question.title}
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            ) : (
              // 显示分类列表
              <div>
                {/* 添加分类按钮 */}
                <button
                  onClick={() => setShowAddCategory(true)}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    marginBottom: '1rem',
                    backgroundColor: '#3182ce',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    fontSize: '0.875rem',
                    fontWeight: '600',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: '0.5rem'
                  }}
                >
                  ➕ 添加问题组
                </button>

                {categories.map((category) => (
                  <div
                    key={category.id}
                    style={{
                      padding: '1rem',
                      margin: '0.5rem 0',
                      backgroundColor: 'white',
                      borderRadius: '8px',
                      border: '1px solid #e2e8f0',
                      transition: 'all 0.2s'
                    }}
                  >
                    <div style={{
                      fontSize: '1rem',
                      fontWeight: '600',
                      color: '#2d3748',
                      marginBottom: '0.5rem',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between'
                    }}>
                      <div 
                        onClick={() => onCategorySelect(category)}
                        style={{ 
                          display: 'flex', 
                          alignItems: 'center', 
                          gap: '0.5rem',
                          cursor: 'pointer',
                          flex: 1
                        }}
                      >
                        📂 {category.name} ({category.questions.length})
                      </div>
                      <div style={{ display: 'flex', gap: '0.5rem' }}>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setEditingCategory(category);
                          }}
                          style={{
                            background: 'none',
                            border: 'none',
                            cursor: 'pointer',
                            fontSize: '1rem',
                            padding: '0.25rem'
                          }}
                          title="编辑分组"
                        >
                          ✏️
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteCategory(category);
                          }}
                          style={{
                            background: 'none',
                            border: 'none',
                            cursor: 'pointer',
                            fontSize: '1rem',
                            padding: '0.25rem'
                          }}
                          title="删除分组"
                        >
                          🗑️
                        </button>
                        <div 
                          onClick={() => onCategorySelect(category)}
                          style={{ 
                            fontSize: '1.5rem',
                            color: '#3182ce',
                            cursor: 'pointer',
                            padding: '0.25rem'
                          }}
                        >
                          ➤
                        </div>
                      </div>
                    </div>
                    <div style={{
                      fontSize: '0.875rem',
                      color: '#718096',
                      lineHeight: '1.4'
                    }}>
                      {category.description}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        ) : (
          // 显示问题列表
          selectedCategory && (
            <div>
              {selectedCategory.questions.map((question) => (
                <div
                  key={question.id}
                  style={{
                    backgroundColor: 'white',
                    border: '1px solid #e2e8f0',
                    borderRadius: '8px',
                    padding: '1rem',
                    marginBottom: '1rem',
                    transition: 'all 0.2s'
                  }}
                >
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start',
                    marginBottom: '0.5rem'
                  }}>
                    <h4 
                      onClick={() => onQuestionSelect(question.content)}
                      style={{
                        margin: 0,
                        fontSize: '1rem',
                        fontWeight: '600',
                        color: '#2d3748',
                        flex: 1,
                        lineHeight: '1.4',
                        cursor: 'pointer'
                      }}
                    >
                      {question.title}
                    </h4>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.25rem',
                        padding: '0.25rem 0.5rem',
                        fontSize: '0.75rem',
                        backgroundColor: getDifficultyColor(question.difficulty),
                        color: 'white',
                        borderRadius: '12px',
                        fontWeight: '500'
                      }}>
                        {getDifficultyInfo(question.difficulty).icon}
                        {getDifficultyInfo(question.difficulty).text}
                      </div>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setEditingQuestion(question);
                        }}
                        style={{
                          background: 'none',
                          border: 'none',
                          cursor: 'pointer',
                          fontSize: '1rem',
                          padding: '0.25rem'
                        }}
                        title="编辑问题"
                      >
                        ✏️
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteQuestion(question);
                        }}
                        style={{
                          background: 'none',
                          border: 'none',
                          cursor: 'pointer',
                          fontSize: '1rem',
                          padding: '0.25rem'
                        }}
                        title="删除问题"
                      >
                        🗑️
                      </button>
                    </div>
                  </div>

                  <p style={{
                    margin: '0 0 0.75rem 0',
                    fontSize: '0.875rem',
                    color: '#4a5568',
                    lineHeight: '1.5'
                  }}>
                    {question.content.length > 100 
                      ? question.content.substring(0, 100) + '...' 
                      : question.content
                    }
                  </p>

                  {question.tags && question.tags.length > 0 && (
                    <div style={{
                      display: 'flex',
                      flexWrap: 'wrap',
                      gap: '0.25rem'
                    }}>
                      {question.tags.map((tag, tagIndex) => (
                        <span
                          key={tagIndex}
                          style={{
                            padding: '0.125rem 0.375rem',
                            fontSize: '0.75rem',
                            backgroundColor: '#edf2f7',
                            color: '#4a5568',
                            borderRadius: '8px'
                          }}
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )
        )}
      </div>

      {/* 添加分类对话框 */}
      {showAddCategory && (
        <AddCategoryDialog
          onSave={handleAddCategory}
          onClose={() => setShowAddCategory(false)}
        />
      )}

      {/* 编辑分类对话框 */}
      {editingCategory && (
        <EditCategoryDialog
          category={editingCategory}
          onSave={handleEditCategory}
          onClose={() => setEditingCategory(null)}
        />
      )}

      {/* 编辑问题对话框 */}
      {editingQuestion && (
        <EditQuestionDialog
          question={editingQuestion}
          categories={categories}
          onSave={handleEditQuestion}
          onClose={() => setEditingQuestion(null)}
        />
      )}
    </div>
  );
}

// 添加分类对话框组件
function AddCategoryDialog({ onSave, onClose }) {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    if (name.trim()) {
      onSave({ name: name.trim(), description: description.trim() });
    }
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0,0,0,0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        padding: '2rem',
        width: '400px',
        maxWidth: '90vw'
      }}>
        <h3 style={{ margin: '0 0 1rem 0' }}>添加问题组</h3>
        <form onSubmit={handleSubmit}>
          <div style={{ marginBottom: '1rem' }}>
            <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600' }}>
              分组名称
            </label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #e2e8f0',
                borderRadius: '4px',
                fontSize: '0.875rem'
              }}
              placeholder="请输入分组名称"
              required
            />
          </div>
          <div style={{ marginBottom: '1.5rem' }}>
            <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600' }}>
              分组描述
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #e2e8f0',
                borderRadius: '4px',
                fontSize: '0.875rem',
                minHeight: '80px',
                resize: 'vertical'
              }}
              placeholder="请输入分组描述"
            />
          </div>
          <div style={{ display: 'flex', gap: '0.5rem', justifyContent: 'flex-end' }}>
            <button
              type="button"
              onClick={onClose}
              style={{
                padding: '0.75rem 1.5rem',
                border: '1px solid #e2e8f0',
                borderRadius: '4px',
                backgroundColor: 'white',
                cursor: 'pointer'
              }}
            >
              取消
            </button>
            <button
              type="submit"
              style={{
                padding: '0.75rem 1.5rem',
                border: 'none',
                borderRadius: '4px',
                backgroundColor: '#3182ce',
                color: 'white',
                cursor: 'pointer'
              }}
            >
              保存
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// 编辑分类对话框组件
function EditCategoryDialog({ category, onSave, onClose }) {
  const [name, setName] = useState(category.name);
  const [description, setDescription] = useState(category.description);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (name.trim()) {
      onSave({ name: name.trim(), description: description.trim() });
    }
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0,0,0,0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        padding: '2rem',
        width: '400px',
        maxWidth: '90vw'
      }}>
        <h3 style={{ margin: '0 0 1rem 0' }}>编辑问题组</h3>
        <form onSubmit={handleSubmit}>
          <div style={{ marginBottom: '1rem' }}>
            <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600' }}>
              分组名称
            </label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #e2e8f0',
                borderRadius: '4px',
                fontSize: '0.875rem'
              }}
              required
            />
          </div>
          <div style={{ marginBottom: '1.5rem' }}>
            <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600' }}>
              分组描述
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #e2e8f0',
                borderRadius: '4px',
                fontSize: '0.875rem',
                minHeight: '80px',
                resize: 'vertical'
              }}
            />
          </div>
          <div style={{ display: 'flex', gap: '0.5rem', justifyContent: 'flex-end' }}>
            <button
              type="button"
              onClick={onClose}
              style={{
                padding: '0.75rem 1.5rem',
                border: '1px solid #e2e8f0',
                borderRadius: '4px',
                backgroundColor: 'white',
                cursor: 'pointer'
              }}
            >
              取消
            </button>
            <button
              type="submit"
              style={{
                padding: '0.75rem 1.5rem',
                border: 'none',
                borderRadius: '4px',
                backgroundColor: '#3182ce',
                color: 'white',
                cursor: 'pointer'
              }}
            >
              保存
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// 编辑问题对话框组件
function EditQuestionDialog({ question, categories, onSave, onClose }) {
  const [title, setTitle] = useState(question.title);
  const [content, setContent] = useState(question.content);
  const [difficulty, setDifficulty] = useState(question.difficulty);
  const [categoryId, setCategoryId] = useState(question.categoryId);
  const [tags, setTags] = useState(question.tags ? question.tags.join(', ') : '');

  const handleSubmit = (e) => {
    e.preventDefault();
    if (title.trim() && content.trim()) {
      onSave({
        title: title.trim(),
        content: content.trim(),
        difficulty,
        categoryId,
        tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag)
      });
    }
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0,0,0,0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        padding: '2rem',
        width: '500px',
        maxWidth: '90vw',
        maxHeight: '90vh',
        overflow: 'auto'
      }}>
        <h3 style={{ margin: '0 0 1rem 0' }}>编辑问题</h3>
        <form onSubmit={handleSubmit}>
          <div style={{ marginBottom: '1rem' }}>
            <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600' }}>
              问题标题
            </label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #e2e8f0',
                borderRadius: '4px',
                fontSize: '0.875rem'
              }}
              required
            />
          </div>

          <div style={{ marginBottom: '1rem' }}>
            <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600' }}>
              问题内容
            </label>
            <textarea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #e2e8f0',
                borderRadius: '4px',
                fontSize: '0.875rem',
                minHeight: '120px',
                resize: 'vertical'
              }}
              required
            />
          </div>

          <div style={{ marginBottom: '1rem' }}>
            <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600' }}>
              所属分组
            </label>
            <select
              value={categoryId}
              onChange={(e) => setCategoryId(e.target.value)}
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #e2e8f0',
                borderRadius: '4px',
                fontSize: '0.875rem'
              }}
            >
              {categories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          <div style={{ marginBottom: '1rem' }}>
            <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600' }}>
              难度等级
            </label>
            <select
              value={difficulty}
              onChange={(e) => setDifficulty(e.target.value)}
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #e2e8f0',
                borderRadius: '4px',
                fontSize: '0.875rem'
              }}
            >
              <option value="easy">简单</option>
              <option value="medium">中等</option>
              <option value="hard">困难</option>
            </select>
          </div>

          <div style={{ marginBottom: '1.5rem' }}>
            <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600' }}>
              标签 (用逗号分隔)
            </label>
            <input
              type="text"
              value={tags}
              onChange={(e) => setTags(e.target.value)}
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #e2e8f0',
                borderRadius: '4px',
                fontSize: '0.875rem'
              }}
              placeholder="例如: 地球化学, 环境科学"
            />
          </div>

          <div style={{ display: 'flex', gap: '0.5rem', justifyContent: 'flex-end' }}>
            <button
              type="button"
              onClick={onClose}
              style={{
                padding: '0.75rem 1.5rem',
                border: '1px solid #e2e8f0',
                borderRadius: '4px',
                backgroundColor: 'white',
                cursor: 'pointer'
              }}
            >
              取消
            </button>
            <button
              type="submit"
              style={{
                padding: '0.75rem 1.5rem',
                border: 'none',
                borderRadius: '4px',
                backgroundColor: '#3182ce',
                color: 'white',
                cursor: 'pointer'
              }}
            >
              保存
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
