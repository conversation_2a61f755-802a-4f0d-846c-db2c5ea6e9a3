import { useState } from 'react';

export default function LeanGptAssistant() {
  const [messages, setMessages] = useState([
    { role: 'system', text: 'Ask about geochemistry logic…' }
  ]);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(false);

  async function handleAsk() {
    if (!input.trim()) return;
    setLoading(true);
    const userMessage = { role: 'user', text: input };
    setMessages((prev) => [...prev, userMessage]);

    const res = await fetch('/api/lean-gpt', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ question: input })
    });

    const data = await res.json();
    const reply = { role: 'assistant', text: data.answer };
    setMessages((prev) => [...prev, reply]);
    setInput('');
    setLoading(false);
  }

  return (
    <div style={{ padding: '2rem', maxWidth: '720px', margin: '0 auto' }}>
      <h2 style={{ fontWeight: 'bold', marginBottom: '1rem' }}>LeanAtom Assistant</h2>
      <div style={{
        marginBottom: '1rem',
        height: '240px',
        overflowY: 'auto',
        border: '1px solid #ccc',
        padding: '1rem',
        borderRadius: '8px'
      }}>
        {messages.map((msg, i) => (
          <div key={i}>
            <b>[{msg.role}]</b> {msg.text}
          </div>
        ))}
      </div>
      <div style={{ display: 'flex', gap: '8px' }}>
        <input
          value={input}
          onChange={e => setInput(e.target.value)}
          placeholder="Ask a scientific modeling question..."
          style={{ flex: 1, padding: '8px' }}
        />
        <button onClick={handleAsk} disabled={loading}>
          {loading ? 'Thinking...' : 'Ask'}
        </button>
      </div>
    </div>
  );
}
