import { OpenAI } from 'openai';
import leanVerifier from '../../lib/leanVerifier.js';
import { getPromptTemplate, detectQuestionType, formatConversationHistory } from '../../lib/promptTemplates.js';

// 初始化 OpenAI 客户端
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export default async function handler(req, res) {
  // 检查请求方法
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { question, conversationHistory = [] } = req.body;

  // 验证输入
  if (!question || typeof question !== 'string') {
    return res.status(400).json({ error: 'Question is required and must be a string' });
  }

  try {
    // 检测问题类型
    const questionType = detectQuestionType(question, conversationHistory);

    // 准备上下文信息
    const context = {
      question: question,
      conversationHistory: formatConversationHistory(conversationHistory)
    };

    // 获取合适的提示词模板
    const prompt = getPromptTemplate(questionType, context);

    const completion = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: '你是一个专业的 Lean 4 数学证明助手，专门帮助用户将自然语言问题转换为 Lean 代码，特别擅长地球化学和环境科学建模。'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: 2000,
      temperature: 0.3,
    });

    const gptResponse = completion.choices[0].message.content;

    // 提取 Lean 代码
    const leanCodeMatch = gptResponse.match(/```lean\n([\s\S]*?)\n```/);
    let leanCode = '';
    let verificationResult = null;
    let syntaxValidation = null;
    let codeInfo = null;

    if (leanCodeMatch) {
      leanCode = leanCodeMatch[1];

      // 语法验证
      syntaxValidation = leanVerifier.validateSyntax(leanCode);

      // 提取代码信息
      codeInfo = leanVerifier.extractInfo(leanCode);

      // Lean 代码验证 (如果启用)
      if (process.env.ENABLE_LEAN_VERIFICATION === 'true') {
        try {
          verificationResult = await leanVerifier.verifyCode(leanCode);
        } catch (error) {
          verificationResult = {
            success: false,
            error: error.message,
            output: ''
          };
        }
      }
    }

    // 返回结果
    res.status(200).json({
      answer: gptResponse,
      leanCode: leanCode,
      questionType: questionType,
      syntaxValidation: syntaxValidation,
      codeInfo: codeInfo,
      verification: verificationResult,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('API Error:', error);

    // 处理不同类型的错误
    if (error.code === 'insufficient_quota') {
      return res.status(429).json({ error: 'OpenAI API quota exceeded' });
    }

    if (error.code === 'invalid_api_key') {
      return res.status(401).json({ error: 'Invalid OpenAI API key' });
    }

    res.status(500).json({
      error: 'Internal server error',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
    });
  }
}
